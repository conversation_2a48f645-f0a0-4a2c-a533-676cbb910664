# Entity Print

Entity Print allows you to print any Drupal entity (Drupal 7, 9 and 10)
or View (Drupal 9 and 10 only) to PDF.

The module is lightweight compared to other modules like the Print
module, has full test coverage and is ready to be used in production
for Drupal 7, 9 and 10.

For a full description of the module, visit the
[project page](https://www.drupal.org/project/entity_print).

Submit bug reports and feature suggestions, or track changes in the
[issue queue](https://www.drupal.org/project/issues/entity_print).


## Table of contents

- Requirements
- Installation
- Configuration
- Maintainers


## Requirements

- Composer


## Installation

Install as you would normally install a contributed Drupal module. For further
information, see
[Installing Drupal Modules](https://www.drupal.org/docs/extending-drupal/installing-drupal-modules).

See documentation at [project page](https://www.drupal.org/project/entity_print) and [usage page](https://www.drupal.org/node/2706755).


## Configuration

1. Enable the module at `Administration > Extend`.
2. To configure an entity print engine, go to Admin ->
Configuration -> Content authoring -> Entity Print.
3. Go to entity manage display and add print option to display.
4. Position the "View PDF" field on "Manage Display" to place a
link to the PDF version of the entity.
5. Visit an entity with the field enabled and click the "View PDF" link.
6. For views, add the "Global: Print" plugin to the header or footer of the view.
7. Set the appropriate permissions to allow various roles to access
the printed entity.


## Maintainers

- Lee Rowlands - [larowlan](https://www.drupal.org/u/larowlan)
- Ben Dougherty - [benjy](https://www.drupal.org/u/benjy)
- Sam Becker - [Sam152](https://www.drupal.org/u/sam152)
- Vladimir Roudakov - [VladimirAus](https://www.drupal.org/u/vladimiraus)
- Attila Santo-Rieder - [attisan](https://www.drupal.org/u/attisan)
- Pamela Barone - [pameeela](https://www.drupal.org/u/pameeela)
