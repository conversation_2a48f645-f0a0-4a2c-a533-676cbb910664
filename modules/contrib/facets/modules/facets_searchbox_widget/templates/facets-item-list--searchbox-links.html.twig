{#
/**
 * @file
 * Default theme implementation for a facets item list.
 *
 * Available variables:
 * - items: A list of items. Each item contains:
 *   - attributes: HTML attributes to be applied to each list item.
 *   - value: The content of the list element.
 * - title: The title of the list.
 * - list_type: The tag for list element ("ul" or "ol").
 * - wrapper_attributes: HTML attributes to be applied to the list wrapper.
 * - attributes: HTML attributes to be applied to the list.
 * - empty: A message to display when there are no items. Allowed value is a
 *   string or render array.
 * - context: A list of contextual data associated with the list. May contain:
 *   - list_style: The ID of the widget plugin this facet uses.
 * - facet: The facet for this result item.
 *   - id: the machine name for the facet.
 *   - label: The facet label.
 *
 * @see facets_preprocess_facets_item_list()
 *
 * @ingroup themeable
 */
#}
<div class="facets-widget- {{- facet.widget.type -}} ">
  <input type="text" placeholder="{{ 'Search...'|t }}" class="facets-widget-searchbox" data-type="links" />
  {% if facet.widget.type %}
    {%- set attributes = attributes.addClass('item-list__' ~ facet.widget.type, 'facets-widget-searchbox-list') %}
  {% endif %}
  {% if items or empty %}
  {%- if title is not empty -%}
    <h3>{{ title }}</h3>
  {%- endif -%}

  {%- if items -%}
  <{{ list_type }}{{ attributes }}>
  {%- for item in items -%}
    <li{{ item.attributes }}>{{ item.value }}</li>
  {%- endfor -%}
</{{ list_type }}>
  {%- else -%}
    {{- empty -}}
    {%- endif -%}
  {%- endif %}

{% if facet.widget.type == "dropdown" %}
  <label id="facet_{{ facet.id }}_label">{{ 'Facet'|t }} {{ facet.label }}</label>
{%- endif %}
<div class="facets-widget-searchbox-no-result hide">{{ 'No result'|t }}</div>
</div>
