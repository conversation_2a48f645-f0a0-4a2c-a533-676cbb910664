<?php

namespace Drupal\facets\Widget;

use <PERSON><PERSON><PERSON>\Core\Cache\CacheBackendInterface;
use <PERSON><PERSON>al\Core\Extension\ModuleHandlerInterface;
use <PERSON>upal\Core\Plugin\DefaultPluginManager;
use Drupal\facets\Annotation\FacetsWidget;

/**
 * Defines a plugin manager for widgets.
 */
class WidgetPluginManager extends DefaultPluginManager {

  /**
   * {@inheritdoc}
   */
  public function __construct(\Traversable $namespaces, CacheBackendInterface $cache_backend, ModuleHandlerInterface $module_handler) {
    parent::__construct('Plugin/facets/widget', $namespaces, $module_handler, WidgetPluginInterface::class, FacetsWidget::class);
    $this->alterInfo('widget_plugin_info');
    $this->setCacheBackend($cache_backend, 'facet_widget_plugins');
  }

}
