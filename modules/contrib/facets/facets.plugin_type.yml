facets_processor:
  label: Facets processor
  plugin_manager_service_id: plugin.manager.facets.processor
  plugin_definition_decorator_class: \Drupal\plugin\PluginDefinition\ArrayPluginDefinitionDecorator

facets_url_processor:
  label: Facets URL processor
  plugin_manager_service_id: plugin.manager.facets.url_processor
  plugin_definition_decorator_class: \Drupal\plugin\PluginDefinition\ArrayPluginDefinitionDecorator

facets_facet_source:
  label: Facets source
  plugin_manager_service_id: plugin.manager.facets.facet_source
  plugin_definition_decorator_class: \Drupal\plugin\PluginDefinition\ArrayPluginDefinitionDecorator

facets_widget:
  label: Facets widget
  plugin_manager_service_id: plugin.manager.facets.widget
  plugin_definition_decorator_class: \Drupal\plugin\PluginDefinition\ArrayPluginDefinitionDecorator

facets_query_type:
  label: Facets query type
  plugin_manager_service_id: plugin.manager.facets.query_type
  plugin_definition_decorator_class: \Drupal\plugin\PluginDefinition\ArrayPluginDefinitionDecorator
