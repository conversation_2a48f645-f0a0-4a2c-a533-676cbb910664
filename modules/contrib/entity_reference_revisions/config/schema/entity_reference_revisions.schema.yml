# Schema for the configuration files of the Entity Reference module.

entity_reference_revisions.default.handler_settings:
  type: mapping
  label: 'View handler settings'
  mapping:
    target_bundles:
      type: sequence
      label: 'types'
      sequence:
        type: string
        label: 'Type'
    sort:
      type: mapping
      label: 'Sort settings'
      mapping:
        field:
          type: string
          label: 'Sort by'
        direction:
          type: string
          label: 'Sort direction'
    filter:
      type: mapping
      label: 'Filter settings'
      mapping:
        type:
          type: string
          label: 'Filter by'
        role:
          type: sequence
          label: 'Restrict to the selected roles'
          sequence:
            type: string
            label: 'Role'
    auto_create:
      type: boolean
      label: 'Create referenced entities if they don''t already exist'

field.storage_settings.entity_reference_revisions:
  type: field.storage_settings.entity_reference

field.value.entity_reference_revisions:
  type: field.value.entity_reference
  label: 'Default value'
  mapping:
    target_id:
      type: integer
      label: 'Value'
    target_revision_id:
      type: integer
      label: 'Revision ID'
    target_uuid:
      type: string
      label: 'Target UUID'

field.field_settings.entity_reference_revisions:
  type: field.field_settings.entity_reference

field.widget.settings.entity_reference_revisions_autocomplete:
  type: field.widget.settings.entity_reference_autocomplete

field.formatter.settings.entity_reference_revisions_entity_view:
  type: mapping
  mapping:
   view_mode:
    type: string
   link:
    type: string

field.formatter.settings.entity_reference_revisions_label:
  type: mapping
  mapping:
    link:
      type: boolean
      label: 'Link label to the referenced entity'
