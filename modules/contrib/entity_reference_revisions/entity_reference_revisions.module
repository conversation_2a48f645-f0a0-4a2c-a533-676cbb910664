<?php

/**
 * @file
 * Provides a field that can reference other entities.
 */

use <PERSON>upal\Component\Utility\NestedArray;
use Dr<PERSON>al\Core\Entity\ContentEntityInterface;
use Dr<PERSON>al\Core\Entity\EntityInterface;
use Dr<PERSON>al\Core\Entity\FieldableEntityInterface;
use Drupal\Core\Entity\TranslatableRevisionableStorageInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Render\Element;
use Drupal\Core\Routing\RouteMatchInterface;
use Drupal\entity_reference_revisions\Plugin\Field\FieldType\EntityReferenceRevisionsItem;
use Drupal\field\Entity\FieldStorageConfig;
use Drupal\field\Entity\FieldConfig;
use Drupal\field\FieldStorageConfigInterface;
use Drupal\Core\Url;

/**
 * Implements hook_help().
 */
function entity_reference_revisions_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.entity_reference_revisions':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Entity Reference Revisions module allows you to create fields that contain links to other entities (such as content items, taxonomy terms, etc.) within the site. This allows you, for example, to include a link to a user within a content item. For more information, see <a href=":er_do">the online documentation for the Entity Reference Revisions module</a> and the <a href=":field_help">Field module help page</a>.', [':field_help' => Url::fromRoute('help.page', ['name' => 'field'])->toString(), ':er_do' => 'https://drupal.org/documentation/modules/entity_reference_revisions']) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Managing and displaying entity reference fields') . '</dt>';
      $output .= '<dd>' . t('The <em>settings</em> and the <em>display</em> of the entity reference field can be configured separately. See the <a href=":field_ui">Field UI help</a> for more information on how to manage fields and their display.', [':field_ui' => Url::fromRoute('help.page', ['name' => 'field_ui'])->toString()]) . '</dd>';
      $output .= '<dt>' . t('Selecting reference type') . '</dt>';
      $output .= '<dd>' . t('In the field settings you can select which entity type you want to create a reference to.') . '</dd>';
      $output .= '<dt>' . t('Filtering and sorting reference fields') . '</dt>';
      $output .= '<dd>' . t('Depending on the chosen entity type, additional filtering and sorting options are available for the list of entities that can be referred to, in the field settings. For example, the list of users can be filtered by role and sorted by name or ID.') . '</dd>';
      $output .= '<dt>' . t('Displaying a reference') . '</dt>';
      $output .= '<dd>' . t('An entity reference can be displayed as a simple label with or without a link to the entity. Alternatively, the referenced entity can be displayed as a teaser (or any other available view mode) inside the referencing entity.') . '</dd>';
      $output .= '</dl>';
      return $output;
  }
}

/**
 * Implements hook_field_widget_info_alter().
 */
function entity_reference_revisions_field_widget_info_alter(&$info) {
  if (isset($info['options_select'])) {
    $info['options_select']['field_types'][] = 'entity_reference_revisions';
  }
  if (isset($info['options_buttons'])) {
    $info['options_buttons']['field_types'][] = 'entity_reference_revisions';
  }
}

/**
 * Implements hook_ENTITY_TYPE_update() for 'field_storage_config'.
 *
 * Reset the instance handler settings, when the target type is changed.
 */
function entity_reference_revisions_field_storage_config_update(FieldStorageConfigInterface $field_storage) {
  if ($field_storage->getType() != 'entity_reference_revisions') {
    // Only act on entity reference fields.
    return;
  }

  if ($field_storage->isSyncing()) {
    // Don't change anything during a configuration sync.
    return;
  }

  if ($field_storage->getSetting('target_type') == $field_storage->original->getSetting('target_type')) {
    // Target type didn't change.
    return;
  }

  if (empty($field_storage->bundles)) {
    // Field storage has no fields.
    return;
  }

  $field_name = $field_storage->getName();

  foreach ($field_storage->bundles() as $entity_type => $bundles) {
    foreach ($bundles as $bundle) {
      $field = FieldConfig::loadByName($entity_type, $bundle, $field_name);
      $field->setSetting('handler_settings', []);
      $field->save();
    }
  }
}

/**
 * Render API callback: Processes the field settings form and allows access to
 * the form state.
 *
 * @see entity_reference_revisions_field_field_settings_form()
 */
function _entity_reference_revisions_field_field_settings_ajax_process($form, FormStateInterface $form_state) {
  _entity_reference_revisions_field_field_settings_ajax_process_element($form, $form);
  return $form;
}

/**
 * Adds entity_reference specific properties to AJAX form elements from the
 * field settings form.
 *
 * @see _entity_reference_revisions_field_field_settings_ajax_process()
 */
function _entity_reference_revisions_field_field_settings_ajax_process_element(&$element, $main_form) {
  if (!empty($element['#ajax'])) {
    $element['#ajax'] = [
      'callback' => 'entity_reference_revisions_settings_ajax',
      'wrapper' => $main_form['#id'],
      'element' => $main_form['#array_parents'],
    ];
  }

  foreach (Element::children($element) as $key) {
    _entity_reference_revisions_field_field_settings_ajax_process_element($element[$key], $main_form);
  }
}

/**
 * Render API callback: Moves entity_reference specific Form API elements
 * (i.e. 'handler_settings') up a level for easier processing by the validation
 * and submission handlers.
 *
 * @see _entity_reference_revisions_field_settings_process()
 */
function _entity_reference_revisions_form_process_merge_parent($element) {
  $parents = $element['#parents'];
  array_pop($parents);
  $element['#parents'] = $parents;
  return $element;
}

/**
 * Form element validation handler; Filters the #value property of an element.
 */
function _entity_reference_revisions_element_validate_filter(&$element, FormStateInterface $form_state) {
  $element['#value'] = array_filter($element['#value']);
  $form_state->setValueForElement($element, $element['#value']);
}

/**
 * Ajax callback for the handler settings form.
 *
 * @see entity_reference_revisions_field_field_settings_form()
 */
function entity_reference_revisions_settings_ajax($form, FormStateInterface $form_state) {
  return NestedArray::getValue($form, $form_state->getTriggeringElement()['#ajax']['element']);
}

/**
 * Submit handler for the non-JS case.
 *
 * @see entity_reference_revisions_field_field_settings_form()
 */
function entity_reference_revisions_settings_ajax_submit($form, FormStateInterface $form_state) {
  $form_state->setRebuild();
}

/**
 * Creates a field of an entity reference revisions field storage on the specified bundle.
 *
 * @param string $entity_type
 *   The type of entity the field will be attached to.
 * @param string $bundle
 *   The bundle name of the entity the field will be attached to.
 * @param string $field_name
 *   The name of the field; if it already exists, a new instance of the existing
 *   field will be created.
 * @param string $field_label
 *   The label of the field.
 * @param string $target_entity_type
 *   The type of the referenced entity.
 * @param string $selection_handler
 *   The selection handler used by this field.
 * @param array $selection_handler_settings
 *   An array of settings supported by the selection handler specified above.
 *   (e.g. 'target_bundles', 'sort', 'auto_create', etc).
 * @param int $cardinality
 *   The cardinality of the field.
 *
 * @see \Drupal\Core\Entity\Plugin\EntityReferenceSelection\SelectionBase::buildConfigurationForm()
 */
function entity_reference_revisions_create_field($entity_type, $bundle, $field_name, $field_label, $target_entity_type, $selection_handler = 'default', $selection_handler_settings = [], $cardinality = 1) {
  // Look for or add the specified field to the requested entity bundle.
  if (!FieldStorageConfig::loadByName($entity_type, $field_name)) {
    \Drupal::entityTypeManager()->getStorage('field_storage_config')->create([
      'field_name' => $field_name,
      'type' => 'entity_reference_revisions',
      'entity_type' => $entity_type,
      'cardinality' => $cardinality,
      'settings' => [
        'target_type' => $target_entity_type,
      ],
    ])->save();
  }
  if (!FieldConfig::loadByName($entity_type, $bundle, $field_name)) {
    \Drupal::entityTypeManager()->getStorage('field_config')->create([
      'field_name' => $field_name,
      'entity_type' => $entity_type,
      'bundle' => $bundle,
      'label' => $field_label,
      'settings' => [
        'handler' => $selection_handler,
        'handler_settings' => $selection_handler_settings,
      ],
    ])->save();
  }
}

/**
 * Implements hook_form_FORM_ID_alter() for 'field_ui_field_storage_add_form'.
 */
function entity_reference_revisions_form_field_ui_field_storage_add_form_alter(array &$form) {
  if (version_compare(\Drupal::VERSION, '10.1.999', '<')) {
    // Move the "Entity reference revisions" option to the end of the list and rename it to
    // "Other".
    unset($form['add']['new_storage_type']['#options'][(string) t('Reference revisions')]['entity_reference_revisions']);
    $form['add']['new_storage_type']['#options'][(string) t('Reference revisions')]['entity_reference_revisions'] = t('Other…');
    return;
  }

  // Changes the entity reference revisions option.
  if (isset($form["group_field_options_wrapper"]["fields"]["entity_reference_revisions"]["#title"])) {
    $form["group_field_options_wrapper"]["fields"]["entity_reference_revisions"]["#title"] = t('Other (revisions)');
    $form["group_field_options_wrapper"]["fields"]["entity_reference_revisions"]["#weight"] = 99;
  }

}

/**
 * Implements hook_field_info_alter().
 */
function entity_reference_revisions_field_info_alter(&$info) {

  if (version_compare(\Drupal::VERSION, '10.1.999', '<')) {
    $info['entity_reference_revisions']['category'] = t('Reference revisions');
  }

}


/**
 * Implements hook_entity_revision_create().
 */
function entity_reference_revisions_entity_revision_create(ContentEntityInterface $new_revision, ContentEntityInterface $entity, $keep_untranslatable_fields) {
  $entity_type_manager = \Drupal::entityTypeManager();
  $storage = \Drupal::entityTypeManager()->getStorage($entity->getEntityTypeId());
  foreach ($entity->getFieldDefinitions() as $field_name => $field_definition) {
    if ($field_definition->getType() == 'entity_reference_revisions' && !$field_definition->isTranslatable()) {
      $target_entity_type_id = $field_definition->getSetting('target_type');
      if ($entity_type_manager->getDefinition($target_entity_type_id)->get('entity_revision_parent_id_field')) {

        // The default implementation copied the values from the current
        // default revision into the field since it is not translatable.
        // Take the originally referenced entity, create a new revision
        // of it and set that instead on the new entity revision.
        $active_langcode = $entity->language()->getId();
        $target_storage = \Drupal::entityTypeManager()->getStorage($target_entity_type_id);
        if ($target_storage instanceof TranslatableRevisionableStorageInterface) {

          $items = $entity->get($field_name);
          $translation_items = NULL;
          if (!$new_revision->isDefaultTranslation() && $storage instanceof TranslatableRevisionableStorageInterface) {
            $translation_items = $items;
            $items = $storage->load($new_revision->id())->get($field_name);
          }

          $values = [];
          foreach ($items as $delta => $item) {
            // If the target entity is missing, let's skip it.
            if (empty($item->entity)) {
              continue;
            }

            // Use the item from the translation if it exists.
            // If we have translation items, use that if one with the matching
            // target id exists.
            if ($translation_items) {
              foreach ($translation_items as $translation_item) {
                if ($item->target_id == $translation_item->target_id) {
                  $item = $translation_item;
                  break;
                }
              }
            }

            /** @var \Drupal\Core\Entity\ContentEntityInterface $target_entity */
            $target_entity = $item->entity;
            if ($target_entity->isTranslatable()) {
              if (!$target_entity->hasTranslation($active_langcode)) {
                $target_entity->addTranslation($active_langcode, $target_entity->toArray());
              }
              $target_entity = $item->entity->getTranslation($active_langcode);
            }
            $revised_entity = $target_storage->createRevision($target_entity, $new_revision->isDefaultRevision(), $keep_untranslatable_fields);

            // Restore the revision ID.
            $revision_key = $revised_entity->getEntityType()->getKey('revision');
            $revised_entity->set($revision_key, $revised_entity->getLoadedRevisionId());
            $values[$delta] = $revised_entity;
          }
          $new_revision->set($field_name, $values);
        }
      }
    }
  }
}

/**
 * Batch callback to dispatch the orphan composite batch operation to a service.
 */
function _entity_reference_revisions_orphan_purger_batch_dispatcher() {
  $args = func_get_args();
  [$service, $method] = explode(':', array_shift($args));
  // The second argument (context) is passed by reference.
  $values = $args[1];
  $args[1] = &$values;
  call_user_func_array([\Drupal::service($service), $method], $args);
}

/**
 * Implements hook_entity_delete().
 *
 * Performs garbage collection for composite entities that were not removed
 * by EntityReferenceRevisionsItem.
 */
function entity_reference_revisions_entity_delete(EntityInterface $entity) {
  if (!$entity instanceof FieldableEntityInterface) {
    return;
  }

  $entity_type_manager = \Drupal::entityTypeManager();
  /** @var \Drupal\Core\Field\FieldTypePluginManager $field_type_manager */
  $field_type_manager = \Drupal::service('plugin.manager.field.field_type');
  foreach ($entity->getFieldDefinitions() as $field_name => $field_definition) {
    $field_class = $field_type_manager->getPluginClass($field_definition->getType());
    if ($field_class == EntityReferenceRevisionsItem::class || is_subclass_of($field_class, EntityReferenceRevisionsItem::class)) {
      $target_entity_type_id = $field_definition->getSetting('target_type');
      $target_entity_storage = $entity_type_manager->getStorage($target_entity_type_id);
      $target_entity_type = $target_entity_storage->getEntityType();

      $parent_type_field = $target_entity_type->get('entity_revision_parent_type_field');
      $parent_id_field = $target_entity_type->get('entity_revision_parent_id_field');
      $parent_name_field = $target_entity_type->get('entity_revision_parent_field_name_field');

      if ($parent_type_field && $parent_id_field && $parent_name_field) {
        $entity_ids = $target_entity_storage
          ->getQuery()
          ->allRevisions()
          ->condition($parent_type_field, $entity->getEntityTypeId())
          ->condition($parent_id_field, $entity->id())
          ->condition($parent_name_field, $field_name)
          ->accessCheck(FALSE)
          ->execute();

        if (empty($entity_ids)) {
          continue;
        }
        $entity_ids = array_unique($entity_ids);
        foreach ($entity_ids as $revision_id => $entity_id) {
          \Drupal::queue('entity_reference_revisions_orphan_purger')->createItem([
            'entity_id' => $entity_id,
            'entity_type_id' => $target_entity_type_id,
          ]);
        }
      }
    }
  }
}
