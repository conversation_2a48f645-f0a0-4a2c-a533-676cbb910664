<?xml version="1.0" encoding="UTF-8"?>

<phpunit bootstrap="tests/bootstrap.php" colors="true">
  <php>
    <!-- Set error reporting to E_ALL. -->
    <ini name="error_reporting" value="32767"/>
    <!-- Do not limit the amount of memory tests take to run. -->
    <ini name="memory_limit" value="-1"/>
  </php>
  <testsuites>
    <testsuite name="Feeds Unit Test Suite">
      <directory>./tests/src/Unit</directory>
    </testsuite>
  </testsuites>
  <listeners>
    <listener class="\Drupal\Tests\Listeners\DrupalListener">
    </listener>
  </listeners>
  <!-- Filter for coverage reports. -->
  <filter>
    <whitelist>
      <directory>./src</directory>
      <!-- By definition test classes have no tests. -->
      <exclude>
        <directory suffix="Test.php">./</directory>
        <directory suffix="TestBase.php">./</directory>
      </exclude>
    </whitelist>
  </filter>
  <logging>
    <log type="coverage-html" target="coverage" showUncoveredFiles="true"/>
  </logging>
</phpunit>
