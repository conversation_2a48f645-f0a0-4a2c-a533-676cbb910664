feeds.admin:
  path: '/admin/content/feed'
  defaults:
    _title: 'Feeds'
    _entity_list: 'feeds_feed'
  requirements:
    _permission: 'access feed overview'

feeds.item_list:
  path: '/feed/{feeds_feed}/list'
  defaults:
    _controller: 'Dr<PERSON>al\feeds\Controller\ItemListController:listItems'
    _title: 'Feed items'
  requirements:
    _entity_access: 'feeds_feed.view'
    feeds_feed: \d+

# Bulk action confirmation pages.
feeds.multiple_clear_confirm:
  path: '/admin/content/feed/clear'
  defaults:
    _form: 'Drupal\feeds\Form\ClearMultipleForm'
  requirements:
    _feeds_feed_clear_multiple_access: 'feeds_feed'

feeds.multiple_delete_confirm:
  path: '/admin/content/feed/delete'
  defaults:
    _form: 'Drupal\feeds\Form\DeleteMultipleForm'
  requirements:
    _feeds_feed_delete_multiple_access: 'feeds_feed'

feeds.multiple_import_confirm:
  path: '/admin/content/feed/import'
  defaults:
    _form: 'Drupal\feeds\Form\ImportMultipleForm'
  requirements:
    _feeds_feed_import_multiple_access: 'feeds_feed'

# Feed entity pages.
entity.feeds_feed.import_form:
  path: '/feed/{feeds_feed}/import'
  defaults:
    _entity_form: 'feeds_feed.import'
  options:
    _admin_route: TRUE
  requirements:
    _entity_access: 'feeds_feed.import'
    feeds_feed: \d+

entity.feeds_feed.schedule_import_form:
  path: '/feed/{feeds_feed}/schedule-import'
  defaults:
    _entity_form: 'feeds_feed.schedule_import'
  options:
    _admin_route: TRUE
  requirements:
    _entity_access: 'feeds_feed.schedule_import'
    feeds_feed: \d+

entity.feeds_feed.clear_form:
  path: '/feed/{feeds_feed}/delete-items'
  defaults:
    _entity_form: 'feeds_feed.clear'
  options:
    _admin_route: TRUE
  requirements:
    _entity_access: 'feeds_feed.clear'
    feeds_feed: \d+

entity.feeds_feed.unlock:
  path: '/feed/{feeds_feed}/unlock'
  defaults:
    _entity_form: 'feeds_feed.unlock'
  options:
    _admin_route: TRUE
  requirements:
    _entity_access: 'feeds_feed.unlock'
    feeds_feed: \d+

# PubSubHubbub.
entity.feeds_feed.receive:
  path: '/feed/{feeds_subscription}/{feeds_push_token}/push_callback'
  defaults:
    _controller: 'Drupal\feeds\Controller\SubscriptionController::receive'
  requirements:
    # External resources should be able to push data to this path.
    _access: 'TRUE'
    feeds_subscription: \d+
  methods: [POST]

entity.feeds_feed.subscribe:
  path: '/feed/{feeds_subscription_id}/{feeds_push_token}/push_callback'
  defaults:
    _controller: 'Drupal\feeds\Controller\SubscriptionController::subscribe'
  requirements:
    # External resources should be able to push data to this path.
    _access: 'TRUE'
    feeds_subscription_id: \d+
  methods: [GET]

# Admin UI routes.
entity.feeds_feed_type.collection:
  path: '/admin/structure/feeds'
  defaults:
    _entity_list: 'feeds_feed_type'
    _title: 'Feed types'
  requirements:
    _permission: 'administer feeds'

entity.feeds_feed_type.mapping:
  path: '/admin/structure/feeds/manage/{feeds_feed_type}/mapping'
  defaults:
    _form: '\Drupal\feeds\Form\MappingForm'
    _title_callback: '\Drupal\feeds\Form\MappingForm::mappingTitle'
  requirements:
    _entity_access: 'feeds_feed_type.mapping'

entity.feeds_feed_type.sources:
  path: '/admin/structure/feeds/manage/{feeds_feed_type}/sources'
  defaults:
    _controller: 'Drupal\feeds\Controller\CustomSourceListController::page'
    _title_callback: '\Drupal\feeds\Controller\CustomSourceListController::title'
  requirements:
    _entity_access: 'feeds_feed_type.mapping'

entity.feeds_feed_type.source_edit:
  path: '/admin/structure/feeds/manage/{feeds_feed_type}/sources/{key}'
  defaults:
    _form: '\Drupal\feeds\Form\CustomSourceEditForm'
    _title_callback: '\Drupal\feeds\Form\CustomSourceEditForm::title'
  requirements:
    _entity_access: 'feeds_feed_type.mapping'

entity.feeds_feed_type.source_delete:
  path: '/admin/structure/feeds/manage/{feeds_feed_type}/sources/{key}/delete'
  defaults:
    _form: '\Drupal\feeds\Form\CustomSourceDeleteForm'
  requirements:
    _entity_access: 'feeds_feed_type.mapping'
