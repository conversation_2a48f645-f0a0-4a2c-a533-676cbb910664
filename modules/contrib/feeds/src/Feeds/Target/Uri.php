<?php

namespace Dr<PERSON>al\feeds\Feeds\Target;

use <PERSON>upal\Core\Field\FieldDefinitionInterface;
use <PERSON>upal\feeds\FieldTargetDefinition;

/**
 * Defines a string field mapper.
 *
 * @FeedsTarget(
 *   id = "uri",
 *   field_types = {"uri"}
 * )
 */
class Uri extends StringTarget {

  /**
   * {@inheritdoc}
   */
  protected static function prepareTarget(FieldDefinitionInterface $field_definition) {
    return FieldTargetDefinition::createFromFieldDefinition($field_definition)
      ->addProperty('value')
      ->markPropertyUnique('value');
  }

}
