<?php

namespace Dr<PERSON>al\feeds\Plugin\Action;

use <PERSON><PERSON><PERSON>\Core\Action\ActionBase;
use <PERSON><PERSON><PERSON>\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON>al\Core\Session\AccountInterface;
use <PERSON><PERSON>al\Core\TempStore\PrivateTempStore;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;

/**
 * Actions for manipulating multiple feeds.
 */
abstract class FeedActionBase extends ActionBase implements ContainerFactoryPluginInterface {

  /**
   * The tempstore object.
   *
   * @var \Drupal\Core\TempStore\PrivateTempStore
   */
  protected $tempStore;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * Constructs a new FeedActionBase object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin ID for the plugin instance.
   * @param array $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\Core\TempStore\PrivateTempStore $temp_store
   *   The tempstore factory.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   */
  public function __construct(array $configuration, $plugin_id, array $plugin_definition, PrivateTempStore $temp_store, AccountInterface $current_user) {
    $this->tempStore = $temp_store;
    $this->currentUser = $current_user;
    parent::__construct($configuration, $plugin_id, $plugin_definition);
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    $temp_store = $container->get('tempstore.private')->get(static::ACTION);

    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $temp_store,
      $container->get('current_user')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function executeMultiple(array $entities) {
    /** @var \Drupal\feeds\FeedInterface[] $entities */
    $selection = [];
    foreach ($entities as $entity) {
      $selection[$entity->id()] = $entity->id();
    }
    $this->tempStore->set($this->currentUser->id() . ':feeds_feed', $selection);
  }

  /**
   * {@inheritdoc}
   */
  public function execute($object = NULL) {
    $this->executeMultiple([$object]);
  }

}
