<?php

namespace Drupal\feeds\Annotation;

/**
 * Defines a Plugin annotation object for Feeds parser plugins.
 *
 * Plugin Namespace: Feeds\Parser.
 *
 * For a working example, see \Drupal\feeds\Feeds\Parser\SyndicationParser.
 *
 * @see \Drupal\feeds\Plugin\Type\FeedsPluginManager
 * @see \Drupal\feeds\Plugin\Type\Parser\ParserInterface
 * @see \Drupal\feeds\Plugin\Type\PluginBase
 * @see plugin_api
 *
 * @Annotation
 */
class FeedsParser extends FeedsBase {
}
