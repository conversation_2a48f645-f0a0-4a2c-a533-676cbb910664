<?php

namespace Drupal\feeds\Annotation;

// phpcs:disable <PERSON><PERSON><PERSON>.NamingConventions.ValidVariableName.LowerCamelName

/**
 * Defines a Plugin annotation object for Feeds source plugins.
 *
 * Plugin Namespace: Feeds\Source.
 *
 * For a working example, see \Drupal\feeds\Feeds\Source\BasicFieldSource.
 *
 * @see \Drupal\feeds\Plugin\Type\FeedsPluginManager
 * @see \Drupal\feeds\Plugin\Type\Source\SourceInterface
 * @see \Drupal\feeds\Plugin\Type\PluginBase
 * @see plugin_api
 *
 * @Annotation
 */
class FeedsSource extends FeedsBase {

  /**
   * The field types a source plugin applies to.
   *
   * @var array
   */
  public $field_types;

  /**
   * The category to which the source plugin belongs.
   *
   * This category gets displayed on the source selector on the mapping form.
   *
   * @var \Drupal\Core\Annotation\Translation
   */
  public $category;

}
