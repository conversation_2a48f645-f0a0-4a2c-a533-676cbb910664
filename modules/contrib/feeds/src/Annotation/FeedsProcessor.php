<?php

namespace Drupal\feeds\Annotation;

/**
 * Defines a Plugin annotation object for Feeds processor plugins.
 *
 * Plugin Namespace: Feeds\Processor.
 *
 * For a working example, see \Drupal\feeds\Feeds\Processor\EntityProcessor.
 *
 * @see \Drupal\feeds\Plugin\Type\FeedsPluginManager
 * @see \Drupal\feeds\Plugin\Type\Processor\ProcessorInterface
 * @see \Drupal\feeds\Plugin\Type\PluginBase
 * @see plugin_api
 *
 * @Annotation
 */
class FeedsProcessor extends FeedsBase {

}
