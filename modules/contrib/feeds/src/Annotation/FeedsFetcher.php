<?php

namespace Drupal\feeds\Annotation;

/**
 * Defines a Plugin annotation object for Feeds fetcher plugins.
 *
 * Plugin Namespace: Feeds\Fetcher.
 *
 * For a working example, see \Drupal\feeds\Feeds\Fetcher\HttpFetcher.
 *
 * @see \Drupal\feeds\Plugin\Type\FeedsPluginManager
 * @see \Drupal\feeds\Plugin\Type\Fetcher\FetcherInterface
 * @see \Drupal\feeds\Plugin\Type\PluginBase
 * @see plugin_api
 *
 * @Annotation
 */
class FeedsFetcher extends FeedsBase {

}
