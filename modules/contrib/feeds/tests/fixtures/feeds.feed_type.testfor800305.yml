uuid: 0ebcd967-4755-4885-a66e-ed1fc234349c
langcode: en
status: true
dependencies:
  config:
    - node.type.article
  module:
    - node
label: 'Article importer'
id: article_importer5
description: ''
help: ''
import_period: 3600
fetcher: http
fetcher_configuration:
  auto_detect_feeds: false
  use_pubsubhubbub: false
  always_download: false
  fallback_hub: ''
  request_timeout: 30
parser: syndication
parser_configuration: {  }
processor: 'entity:node'
processor_configuration:
  values:
    type: article
  langcode: en
  insert_new: 1
  update_existing: 0
  update_non_existent: _keep
  skip_hash_check: false
  authorize: true
  expire: -1
  owner_feed_author: false
  owner_id: 0
custom_sources: {  }
mappings: {  }
