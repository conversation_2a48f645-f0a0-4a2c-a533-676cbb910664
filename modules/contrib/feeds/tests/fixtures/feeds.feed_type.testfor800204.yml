uuid: 91c601fd-ce86-499e-b15e-bbd9262a90d4
langcode: en
status: true
dependencies:
  config:
    - node.type.article
  module:
    - node
label: Article importer
id: article_importer4
description: ''
help: ''
import_period: 3600
fetcher: http
fetcher_configuration:
  auto_detect_feeds: false
  use_pubsubhubbub: false
  fallback_hub: ''
  request_timeout: 30
parser: syndication
parser_configuration: {  }
processor: 'entity:node'
processor_configuration:
  update_existing: 0
  update_non_existent: 'entity:unpublish_action:node'
  expire: -1
  owner_feed_author: false
  owner_id: 0
  authorize: true
  skip_hash_check: false
  values:
    type: article
custom_sources: {  }
mappings: {  }
