<?xml version="1.0" encoding="utf-8" ?>
<feed xmlns="http://www.w3.org/2005/Atom" xml:lang="en">
  <title>Feeds issue #1281496</title>
  <link rel="self" type="application/atom+xml" href="http://www.example.com/feeds/entries.atom"/>
  <link rel="alternate" type="text/html" href="http://www.example.com/node/1281496"/>
  <updated>2016-10-29T20:35:56Z</updated>
  <author>
    <name>dcotruta</name>
  </author>
  <id>http://www.example.com/node/1281496</id>

  <entry>
    <title>Re-spin the patch</title>
    <link rel="alternate" type="text/html" href="node/1281496#comment-11669575" />
    <id>comment-11669575</id>
    <published>2016-09-28T17:08:00Z</published>
    <updated>2016-10-29T15:49:00Z</updated>
    <summary>Re-spin the patch for feeds 7.x-2.0-beta2.</summary>
    <author>
      <name>natew</name>
    </author>
  </entry>

  <entry>
    <title>Thanks twistor, I just tried the latest patch</title>
    <link rel="alternate" type="text/html" href="node/1281496#comment-10080648" />
    <id>comment-10080648</id>
    <published>2015-07-02T19:33:00Z</published>
    <updated>2015-07-02T19:33:00Z</updated>
    <summary>Thanks twistor, I just tried the latest patch and this works for me. The feed items get imported and the proper url is set.</summary>
    <author>
      <name>natew</name>
    </author>
  </entry>

  <entry>
    <title>Probably missed a string cast somewhere.</title>
    <link rel="alternate" type="text/html" href="node/1281496#comment-10062564" />
    <id>comment-10062564</id>
    <published>2015-06-26T19:52:00Z</published>
    <updated>2015-06-28T20:00:00Z</updated>
    <summary>Probably missed a string cast somewhere.</summary>
    <author>
      <name>twistor</name>
    </author>
  </entry>

</feed>
