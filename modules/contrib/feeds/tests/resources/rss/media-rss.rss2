<?xml version="1.0" encoding="utf-8" ?>
<rss xmlns:media="http://search.yahoo.com/mrss/" version="2.0">
  <channel>
    <title>Media RSS</title>
    <link>https://www.example.com</link>
    <description>Items with media content</description>
    <language>en</language>
    <item>
      <title>Add support for Media RSS</title>
      <link>https://www.drupal.org/project/feeds/issues/3049802</link>
      <description>Media RSS is a new RSS module that supplements the &lt;enclosure&gt; capabilities of RSS 2.0. RSS enclosures are already being used to syndicate audio files and images. Media RSS extends enclosures to handle other media types, such as short films or TV, as well as provide additional metadata with the media. Media RSS enables content publishers and bloggers to syndicate multimedia content such as TV and video clips, movies, images and audio.</description>
      <pubDate>Sun, 21 Apr 2019 19:48:50 +0200</pubDate>
      <dc:creator>SocialNicheGuru</dc:creator>
      <guid isPermaLink="true">https://www.drupal.org/project/feeds/issues/3049802</guid>
    </item>
    <item>
      <title>Media content and thumbnail</title>
      <link>https://www.example.com/media-content-thumbnail</link>
      <description></description>
      <pubDate>Sun, 21 Apr 2019 19:48:50 +0200</pubDate>
      <guid isPermaLink="true">https://www.example.com/media-content-thumbnail</guid>
      <media:content url="https://www.example.com/image1.png" medium="image" width="640" height="424" />
      <media:thumbnail url="https://www.example.com/thumbnail1.png" width="80" height="53" />
    </item>
    <item>
      <title>Without thumbnail</title>
      <link>https://www.example.com/without-thumbnail</link>
      <description></description>
      <pubDate>Sun, 21 Apr 2019 19:48:50 +0200</pubDate>
      <guid isPermaLink="true">https://www.example.com/without-thumbnail</guid>
      <media:content url="https://www.example.com/image2.png" medium="image" width="640" height="424" />
    </item>
    <item>
      <title>Thumbnail only</title>
      <link>https://www.example.com/thumbnail-only</link>
      <description></description>
      <pubDate>Sun, 21 Apr 2019 19:48:50 +0200</pubDate>
      <guid isPermaLink="true">https://www.example.com/thumbnail-only</guid>
      <media:thumbnail url="https://www.example.com/thumbnail3.png" width="80" height="53" />
    </item>
    <item>
      <title>Media content and description</title>
      <link>https://www.example.com/media-content-description</link>
      <description></description>
      <pubDate>Sun, 21 Apr 2019 19:48:50 +0200</pubDate>
      <guid isPermaLink="true">https://www.example.com/media-content-description</guid>
      <media:content url="https://www.example.com/image3.png" medium="image" width="640" height="424" />
      <media:description>Example media description</media:description>
    </item>
    <item>
      <title>Media description only</title>
      <link>https://www.example.com/media-content-description-only</link>
      <description></description>
      <pubDate>Sun, 21 Apr 2019 19:48:50 +0200</pubDate>
      <guid isPermaLink="true">https://www.example.com/media-content-description-only</guid>
      <media:description>Example media description</media:description>
    </item>
  </channel>
</rss>