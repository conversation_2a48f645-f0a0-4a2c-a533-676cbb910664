# Parser configurations.
feeds.parser.parser_with_foo_source:
  type: mapping
  label: 'Parser with Foo Source parser settings'

feeds.parser.parser_with_mapping_form:
  type: mapping
  label: 'Parser with mapping form parser settings'
  mapping:
    dummy:
      type: string

# Custom source configurations.
feeds.custom_source.foo:
  type: mapping
  mapping:
    value:
      type: string
    label:
      type: string
    machine_name:
      type: string
    type:
      type: string
    propbool:
      type: boolean
    proptext:
      type: string

# Custom third party settings.
feeds.feed_type.*.third_party.feeds_test_plugin:
  type: mapping
  label: 'Foo settings'
  mapping:
    status:
      type: boolean
