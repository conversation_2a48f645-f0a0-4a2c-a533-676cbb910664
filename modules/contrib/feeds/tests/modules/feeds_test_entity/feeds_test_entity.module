<?php

/**
 * @file
 * Hook implementations.
 */

use Drupal\Core\Entity\EntityTypeInterface;
use Drupal\Core\Field\BaseFieldDefinition;

/**
 * Implements hook_entity_base_field_info().
 */
function feeds_test_entity_entity_base_field_info(EntityTypeInterface $entity_type) {
  $fields = [];

  if ($entity_type->id() === 'feeds_test_entity_test_no_links' && \Drupal::state()->get('entity_test.boolean_field')) {
    $fields['boolean_field'] = BaseFieldDefinition::create('boolean')
      ->setLabel('Boolean field')
      ->setDefaultValue(FALSE)
      ->setRequired(FALSE)
      ->setRevisionable(TRUE);
  }

  return $fields;
}
