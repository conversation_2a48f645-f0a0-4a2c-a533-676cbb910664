services:
  # Access handler.
  feeds_log.access_handler:
    class: \Drupal\feeds_log\Access\FeedLogAccess

  # File manager.
  feeds_log.file_manager:
    class: \Drupal\feeds_log\LogFileManager
    arguments: ['@config.factory', '@file_system', '@stream_wrapper_manager']

  # Loggers.
  logger.channel.feeds_log:
    parent: logger.channel_base
    arguments: ['feeds_log']

  # Event subscribers.
  feeds_log.report_subscriber:
    class: \Drupal\feeds_log\EventSubscriber\FeedReportSubscriber
    arguments: ['@entity_type.manager', '@config.factory', '@event_dispatcher', '@logger.log_message_parser', '@datetime.time']
    tags:
      - { name: event_subscriber }

  feeds_log.stampede_subscriber:
    class: \Drupal\feeds_log\EventSubscriber\StampedeSubscriber
    arguments: ['@logger.channel.feeds_log']
    tags:
      - { name: event_subscriber }
