<?php

namespace Drupal\feeds_log\Plugin\Field\FieldFormatter;

use <PERSON><PERSON><PERSON>\Core\Field\FieldDefinitionInterface;
use <PERSON><PERSON>al\Core\Field\FieldItemListInterface;
use <PERSON><PERSON>al\Core\Field\FormatterBase;
use <PERSON><PERSON>al\Core\File\FileUrlGeneratorInterface;
use Dr<PERSON>al\Core\Url;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Plugin implementation of the 'feeds_log_file_uri_link' formatter.
 *
 * @FieldFormatter(
 *   id = "feeds_log_file_uri_link",
 *   label = @Translation("Link to file"),
 *   field_types = {
 *     "uri",
 *   }
 * )
 */
class FileUriLinkFormatter extends FormatterBase {

  /**
   * The file URL generator.
   *
   * @var \Drupal\Core\File\FileUrlGeneratorInterface
   */
  protected $fileUrlGenerator;

  /**
   * Constructs a new FileUriLinkFormatter object.
   *
   * @param string $plugin_id
   *   The plugin_id for the formatter.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\Core\Field\FieldDefinitionInterface $field_definition
   *   The definition of the field to which the formatter is associated.
   * @param array $settings
   *   The formatter settings.
   * @param string $label
   *   The formatter label display setting.
   * @param string $view_mode
   *   The view mode.
   * @param array $third_party_settings
   *   Any third party settings.
   * @param \Drupal\Core\File\FileUrlGeneratorInterface $file_url_generator
   *   The file URL generator.
   */
  public function __construct($plugin_id, $plugin_definition, FieldDefinitionInterface $field_definition, array $settings, $label, $view_mode, array $third_party_settings, FileUrlGeneratorInterface $file_url_generator) {
    parent::__construct($plugin_id, $plugin_definition, $field_definition, $settings, $label, $view_mode, $third_party_settings);
    $this->fileUrlGenerator = $file_url_generator;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $plugin_id,
      $plugin_definition,
      $configuration['field_definition'],
      $configuration['settings'],
      $configuration['label'],
      $configuration['view_mode'],
      $configuration['third_party_settings'],
      $container->get('file_url_generator'),
    );
  }

  /**
   * {@inheritdoc}
   */
  public function viewElements(FieldItemListInterface $items, $langcode) {
    $elements = [];

    foreach ($items as $delta => $item) {
      if (!$item->isEmpty()) {
        try {
          $elements[$delta] = [
            '#type' => 'link',
            '#url' => Url::fromUri($this->fileUrlGenerator->generateAbsoluteString($item->value)),
            '#title' => $item->value,
            '#attributes' => [
              'target' => '_blank',
            ],
          ];
        }
        catch (\Exception $e) {
          // Generating a link failed, display uri as plain text instead.
          $elements[$delta] = [
            '#type' => 'plain_text',
            '#plain_text' => $item->value,
          ];
        }
      }
    }

    return $elements;
  }

}
