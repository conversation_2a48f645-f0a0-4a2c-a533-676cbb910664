/* Feeds admin overview form. */
table.feeds-admin-feed-type thead th {
  border: none;
}

table.feeds-admin-feed-type td.disabled {
  color: #aaa;
}

table.feeds-admin-feed-type tr.disabled.odd,
table.feeds-admin-feed-type tr.disabled.even {
  border-color: #eee;
}

table.feeds-admin-feed-type tr.disabled.odd {
  background-color: #f5f5f5;
}

/* Feeds edit form layout. */
div.left-bar {
  position: relative;
  float: left;
  width: 240px;
  padding: 10px 10px 0 0;
  border-right: 1px solid #ddd;
}

div.configuration {
  margin-left: -240px;
  padding: 10px 0 0 250px;
}

div.configuration-squeeze {
  margin-left: 250px;
}

/* Container theming. */
div.feeds-container h4 {
  font-size: 1.2em;
  font-weight: bold;
}

div.feeds-container.plain {
  margin: 10px 0;
  padding: 5px;
  border-top: 2px solid #ddd;
  border-bottom: 1px solid #ddd;
  background-color: #eee;
}

div.feeds-container.plain h4 {
  margin: 0;
  padding: 0;
  font-size: 1.0em;
}

div.feeds-container-body p {
  margin: 0;
  padding: 5px 0;
}

div.feeds-container-body div.item-list ul {
  margin: 0;
}

div.feeds-container-body div.item-list ul li {
  margin: 0;
  padding: 0;
  list-style-type: none;
  background-image: none;
}

ul.container-actions {
  float: right;
  margin: 0;
  font-family: Arial, Helvetica, sans-serif;
}

ul.container-actions li {
  position: relative; /* Fix for IE 7 compatibility mode. */
  margin: 0;
  padding: 0;
  list-style-type: none;
  text-align: right;
  background-image: none;
}

ul.container-actions .form-item,
ul.container-actions li form,
ul.container-actions li form input {
  display: inline;
  padding: 0;
}
