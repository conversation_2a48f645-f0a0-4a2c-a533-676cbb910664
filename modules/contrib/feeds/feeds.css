.feeds-plugin-inline {
  float: left;
  margin-right: 3em;
}

.feeds-mapping-form td .form-item.form-type-select {
  margin-bottom: 10px;
}

.feeds-mapping-form .item-list ul li {
  margin: 0;
  list-style: none;
  list-style-type: none;
}

.feeds-mapping-form .feeds-mapping-settings-editing {
  background: #d5e9f2;
}

.feeds-mapping-form tr.mapping-property {
  border-bottom: none;
}
.feeds-mapping-form tr.mapping-property td {
  padding: 0.2rem 1rem;
  vertical-align: middle;
  border: 0;
}
.feeds-mapping-form tr.mapping-property-last {
  border-bottom: 3px double #e6e4df;
}
.feeds-mapping-form tr.mapping-property-first td {
  padding-top: 0.5rem;
}
.feeds-mapping-form tr.mapping-property-last td {
  padding-bottom: 0.5rem;
}

.feeds-feed-type-secondary-settings {
  clear: both;
}

.missing-target .target::before {
  float: left;
  width: 16px;
  height: 16px;
  margin-right: .5em;
  content: url("images/error.svg");
}
