<?php

/**
 * @file
 * Define the hook related to email attachment.
 */

/**
 * Implements hook_mail_alter().
 *
 * This is an example implementation to show how to alter emails generated by
 * other modules. Go to the URL /contact to send an email. It will arrive with
 * this file attached.
 */
function email_attachment_demo_mail_alter(&$message) {
  if ($message['id'] != 'contact_page_mail') {
    return;
  }
  $message['params'] += [
    'attachment' => [
      'filename' => __FILE__,
    ],
  ];
}
