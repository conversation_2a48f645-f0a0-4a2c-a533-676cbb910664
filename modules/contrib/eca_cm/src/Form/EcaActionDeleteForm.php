<?php

namespace Dr<PERSON>al\eca_cm\Form;

use <PERSON><PERSON><PERSON>\Component\Render\MarkupInterface;

/**
 * Form for deleting an ECA action.
 */
class EcaActionDeleteForm extends EcaPluginDeleteForm {

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'eca_cm_action_delete';
  }

  /**
   * {@inheritdoc}
   */
  protected string $type = 'action';

  /**
   * {@inheritdoc}
   */
  protected function getTypeLabel(): MarkupInterface {
    return $this->t('Action');
  }

}
