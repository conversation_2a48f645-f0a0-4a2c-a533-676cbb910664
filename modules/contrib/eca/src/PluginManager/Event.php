<?php

namespace Dr<PERSON>al\eca\PluginManager;

use <PERSON><PERSON><PERSON>\Core\Cache\CacheBackendInterface;
use <PERSON><PERSON><PERSON>\Core\Extension\ModuleHandlerInterface;
use <PERSON><PERSON>al\Core\Plugin\DefaultPluginManager;
use <PERSON><PERSON>al\eca\Annotation\EcaEvent;
use <PERSON><PERSON>al\eca\Plugin\ECA\Event\EventInterface;

/**
 * ECA event plugin manager.
 */
class Event extends DefaultPluginManager {

  /**
   * Constructs PluginManager object.
   *
   * @param \Traversable $namespaces
   *   An object that implements \Traversable which contains the root paths
   *   keyed by the corresponding namespace to look for plugin implementations.
   * @param \Drupal\Core\Cache\CacheBackendInterface $cache_backend
   *   Cache backend instance to use.
   * @param \Drupal\Core\Extension\ModuleHandlerInterface $module_handler
   *   The module handler to invoke the alter hook with.
   */
  public function __construct(\Traversable $namespaces, CacheBackendInterface $cache_backend, ModuleHandlerInterface $module_handler) {
    parent::__construct(
      'Plugin/ECA/Event',
      $namespaces,
      $module_handler,
      EventInterface::class,
      EcaEvent::class
    );
    $this->alterInfo('eca_event_info');
    $this->setCacheBackend($cache_backend, 'eca_event_plugins', ['eca_event_plugins']);
  }

}
