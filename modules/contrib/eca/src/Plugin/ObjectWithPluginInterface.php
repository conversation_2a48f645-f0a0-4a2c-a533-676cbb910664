<?php

namespace <PERSON><PERSON>al\eca\Plugin;

use <PERSON><PERSON><PERSON>\Component\Plugin\PluginInspectionInterface;

/**
 * An interface implemented by objects that hold a plugin instance.
 */
interface ObjectWithPluginInterface {

  /**
   * Get the plugin instance.
   *
   * @return \Drupal\Component\Plugin\PluginInspectionInterface
   *   The plugin instance.
   */
  public function getPlugin(): PluginInspectionInterface;

}
