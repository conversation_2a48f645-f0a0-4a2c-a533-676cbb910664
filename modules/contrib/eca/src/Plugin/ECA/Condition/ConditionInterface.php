<?php

namespace Dr<PERSON>al\eca\Plugin\ECA\Condition;

use <PERSON><PERSON>al\Component\Plugin\ConfigurableInterface;
use <PERSON><PERSON>al\Component\Plugin\DependentPluginInterface;
use <PERSON><PERSON><PERSON>\Component\Plugin\PluginInspectionInterface;
use <PERSON><PERSON><PERSON>\Core\Cache\CacheableDependencyInterface;
use Dr<PERSON>al\Core\Plugin\ContextAwarePluginInterface;
use Dr<PERSON>al\Core\Plugin\PluginFormInterface;

/**
 * Interface for ECA provided conditions.
 */
interface ConditionInterface extends PluginFormInterface, ConfigurableInterface, DependentPluginInterface, PluginInspectionInterface, CacheableDependencyInterface, ContextAwarePluginInterface {

  /**
   * Resets stateful variables to their initial values.
   *
   * @return \Drupal\eca\Plugin\ECA\Condition\ConditionInterface
   *   This.
   */
  public function reset(): ConditionInterface;

  /**
   * Determines whether condition result will be negated.
   *
   * @return bool
   *   Whether the condition result will be negated.
   */
  public function isNegated(): bool;

  /**
   * Evaluates the condition and returns TRUE or FALSE accordingly.
   *
   * @return bool
   *   TRUE if the condition has been met, FALSE otherwise.
   */
  public function evaluate(): bool;

  /**
   * Sets the event that triggered the process in which this condition occurs.
   *
   * @param \Drupal\Component\EventDispatcher\Event|\Symfony\Contracts\EventDispatcher\Event $event
   *   The triggering event.
   *
   * @return $this
   */
  public function setEvent(object $event): ConditionInterface;

  /**
   * Gets the event that triggered the process in which this condition occurs.
   *
   * @return \Drupal\Component\EventDispatcher\Event|\Symfony\Contracts\EventDispatcher\Event
   *   The triggering event.
   */
  public function getEvent(): object;

}
