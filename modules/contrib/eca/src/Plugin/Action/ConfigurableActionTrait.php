<?php

namespace Drupal\eca\Plugin\Action;

use <PERSON>upal\Component\Utility\NestedArray;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Trait providing methods for ECA provided action plugins.
 */
trait ConfigurableActionTrait {

  /**
   * {@inheritdoc}
   */
  public function getConfiguration(): array {
    return $this->configuration;
  }

  /**
   * {@inheritdoc}
   */
  public function setConfiguration(array $configuration): void {
    $this->configuration = NestedArray::mergeDeep(
      $this->defaultConfiguration(),
      $configuration
    );
  }

  /**
   * {@inheritdoc}
   */
  public function validateConfigurationForm(array &$form, FormStateInterface $form_state): void {
  }

  /**
   * {@inheritdoc}
   */
  public function calculateDependencies(): array {
    return [];
  }

}
