<?php

namespace Drupal\eca;

use <PERSON>upal\Core\DependencyInjection\ContainerBuilder;
use Drupal\Core\DependencyInjection\ServiceProviderBase;
use Drupal\eca\Token\ContribToken;

/**
 * Provider for dynamically provided services by ECA.
 */
class EcaServiceProvider extends ServiceProviderBase {

  /**
   * {@inheritdoc}
   */
  public function alter(ContainerBuilder $container): void {
    if (class_exists('Drupal\token\Token')) {
      // Replace the core decorator with the contrib decorator.
      $definition = $container->getDefinition('eca.service.token');
      $definition->setClass(ContribToken::class);
    }
  }

}
