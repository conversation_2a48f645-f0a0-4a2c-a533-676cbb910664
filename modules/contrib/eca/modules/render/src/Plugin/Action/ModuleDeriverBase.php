<?php

namespace Dr<PERSON>al\eca_render\Plugin\Action;

use <PERSON><PERSON><PERSON>\Component\Plugin\Derivative\DeriverBase;
use <PERSON><PERSON><PERSON>\Core\Extension\ModuleHandlerInterface;
use <PERSON><PERSON>al\Core\Plugin\Discovery\ContainerDeriverInterface;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;

/**
 * Base class for derivers that depend on installed modules.
 */
abstract class ModuleDeriverBase extends DeriverBase implements ContainerDeriverInterface {

  /**
   * A list of modules, that are required.
   *
   * @var array
   */
  protected static array $requiredModules = [];

  /**
   * The module handler.
   *
   * @var \Drupal\Core\Extension\ModuleHandlerInterface
   */
  protected ModuleHandlerInterface $moduleHandler;

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, $base_plugin_id) {
    $instance = new static();
    $instance->moduleHandler = $container->get('module_handler');
    return $instance;
  }

  /**
   * {@inheritdoc}
   */
  public function getDerivativeDefinitions($base_plugin_definition): array {
    if (empty($this->derivatives)) {
      $required_modules_installed = TRUE;
      foreach (static::$requiredModules as $name) {
        if (!$this->moduleHandler->moduleExists($name)) {
          $required_modules_installed = FALSE;
          break;
        }
      }
      if ($required_modules_installed) {
        $this->buildDerivativeDefinitions($base_plugin_definition);
      }
    }
    return $this->derivatives;
  }

  /**
   * Builds up the derivative definitions.
   *
   * The definitions are to be added to the "derivatives" property.
   *
   * @param array $base_plugin_definition
   *   The definition array of the base plugin.
   */
  abstract protected function buildDerivativeDefinitions(array $base_plugin_definition): void;

}
