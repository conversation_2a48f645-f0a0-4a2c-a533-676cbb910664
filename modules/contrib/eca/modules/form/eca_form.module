<?php

/**
 * @file
 * ECA Form submodule.
 */

use Drupal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\eca_form\HookHandler;

/**
 * Helper function to return the hook handler service.
 *
 * @return \Drupal\eca_form\HookHandler
 *   The hook handler service.
 */
function _eca_form_hook_handler(): <PERSON><PERSON>andler {
  return \Drupal::service('eca_form.hook_handler');
}

/**
 * Implements hook_form_alter().
 */
function eca_form_form_alter(&$form, FormStateInterface $form_state) {
  _eca_form_hook_handler()->alter($form, $form_state);
}

/**
 * Implements hook_inline_entity_form_entity_form_alter().
 */
function eca_form_inline_entity_form_entity_form_alter(array &$entity_form, FormStateInterface $form_state) {
  _eca_form_hook_handler()->alter($entity_form, $form_state);
}
