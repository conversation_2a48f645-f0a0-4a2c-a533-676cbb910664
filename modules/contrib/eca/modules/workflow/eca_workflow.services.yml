services:
  eca_workflow.subscriber:
    class: <PERSON>upal\eca_workflow\EventSubscriber\EcaWorkflow
    parent: eca.default_event_subscriber
    tags:
      - { name: event_subscriber }
  eca_workflow.hook_handler:
    class: Drupal\eca_workflow\HookHandler
    arguments: ['@eca.trigger_event']
    calls:
      - [setContentEntityTypes, ['@eca.service.content_entity_types']]
      - [setModerationInformation, ['@content_moderation.moderation_information']]
