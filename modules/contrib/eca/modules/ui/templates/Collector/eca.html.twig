{% block toolbar %}
    {# ECA Information #}
    {% set icon %}
        <a href="{{ url("webprofiler.dashboard", {profile: token}, {fragment: 'eca'}) }}"
           title="{{ 'ECA'|t }}">
            <img width="32" height="32" alt="{{ 'ECA'|t }}"
                 src="data:image/png;base64,{{ collector.icon }}">
        </a>
    {% endset %}
    {% set text %}
        {% apply spaceless %}
            <div class="sf-toolbar-info-piece sf-toolbar-info-php-ext">
                <b>{{ 'Log entries'|t }}</b>
                <span>{{ collector.numberoflogentries }}</span>
            </div>
            <div class="sf-toolbar-info-piece">
                <b>Token</b>
                <span>{{ token }}</span>
            </div>
        {% endapply %}
    {% endset %}

    <div class="sf-toolbar-block">
        <div class="sf-toolbar-icon">{{ icon|default('') }}</div>
        <div class="sf-toolbar-info">{{ text|default('') }}</div>
    </div>
{% endblock %}

{% block panel %}
    <script id="eca" type="text/template">
        <h2 class="panel__title">{{ 'ECA'|t }}</h2>
        <div class="panel__container">
            <table class="table--duo">
                <% _.each( data, function( item, key ){ %>
                <tr>
                    <th><%= Drupal.webprofiler.helpers.frm(item['message']) %></th>
                    <td><%= Drupal.webprofiler.helpers.frm(item['tokens']) %></td>
                </tr>
                <% }); %>
            </table>
        </div>
    </script>
{% endblock %}
