langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_other_node
    - node.type.type_1
    - node.type.type_2
id: node.type_1.field_other_node
field_name: field_other_node
entity_type: node
bundle: type_1
label: 'Other node'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      type_2: type_2
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
