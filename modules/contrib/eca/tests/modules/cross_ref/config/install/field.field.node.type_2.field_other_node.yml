langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_other_node
    - node.type.type_1
    - node.type.type_2
id: node.type_2.field_other_node
field_name: field_other_node
entity_type: node
bundle: type_2
label: 'Other node'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      type_1: type_1
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
