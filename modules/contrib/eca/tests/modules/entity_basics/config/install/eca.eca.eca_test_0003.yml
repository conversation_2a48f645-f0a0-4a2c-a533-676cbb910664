langcode: en
status: true
dependencies:
  module:
    - eca_content
id: eca_test_0003
modeller: bpmn_io
label: 'Entity Events Part 2'
version: 1.0.0
weight: null
events:
  Event_0865lkv:
    plugin: 'content_entity:custom'
    label: C2
    configuration:
      event_id: C2
    successors:
      -
        id: Activity_1ciefod
        condition: ''
  Event_1x9pqyk:
    plugin: 'content_entity:custom'
    label: C3
    configuration:
      event_id: C3
    successors:
      -
        id: Activity_0c0cfkg
        condition: ''
conditions: {  }
gateways: {  }
actions:
  Activity_1ciefod:
    plugin: action_message_action
    label: Msg
    configuration:
      replace_tokens: false
      message: 'Message 2: [entity:title]'
    successors: {  }
  Activity_0c0cfkg:
    plugin: action_message_action
    label: Msg
    configuration:
      replace_tokens: false
      message: 'Message 3: [entity:name]'
    successors: {  }
