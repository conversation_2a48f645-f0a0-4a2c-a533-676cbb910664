langcode: en
status: true
dependencies:
  config:
    - eca.eca.eca_test_0006
id: eca_test_0006
label: 'Write Log Message'
tags:
  - 'test models'
  - logging
documentation: 'Write log messages.'
filename: ''
modeldata: |
  <?xml version="1.0" encoding="UTF-8"?>
  <bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:qa="http://some-company/schema/bpmn/qa" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="eca_test_0006" name="Write Log Message" isExecutable="true" camunda:versionTag="1.0.0">
      <bpmn2:documentation>Write log messages.</bpmn2:documentation>
      <bpmn2:extensionElements>
        <camunda:properties>
          <camunda:property name="Tags" value="test models,logging"/>
        </camunda:properties>
      </bpmn2:extensionElements>
      <bpmn2:startEvent id="Event_1eshshx" name="Presave node" camunda:modelerTemplate="org.drupal.event.content_entity:presave">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="content_entity:presave"/>
          </camunda:properties>
          <camunda:field name="type">
            <camunda:string>node _all</camunda:string>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:outgoing>Flow_0bcpf87</bpmn2:outgoing>
      </bpmn2:startEvent>
      <bpmn2:task id="Activity_02f0tls" name="Log" camunda:modelerTemplate="org.drupal.action.eca_write_log_message">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="eca_write_log_message"/>
          </camunda:properties>
          <camunda:field name="channel">
            <camunda:string>eca</camunda:string>
          </camunda:field>
          <camunda:field name="severity">
            <camunda:string>6</camunda:string>
          </camunda:field>
          <camunda:field name="message">
            <camunda:string>Node [entity:title] is about to be saved</camunda:string>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:incoming>Flow_0bcpf87</bpmn2:incoming>
      </bpmn2:task>
      <bpmn2:sequenceFlow id="Flow_0bcpf87" sourceRef="Event_1eshshx" targetRef="Activity_02f0tls"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="sid-1cabc76d-d88d-4356-9904-af226630ab20">
      <bpmndi:BPMNPlane id="sid-46a4a3ff-1826-420e-b0f0-4666d0e322a8" bpmnElement="eca_test_0006">
        <bpmndi:BPMNEdge id="Flow_0bcpf87_di" bpmnElement="Flow_0bcpf87">
          <di:waypoint x="328" y="120"/>
          <di:waypoint x="380" y="120"/>
        </bpmndi:BPMNEdge>
        <bpmndi:BPMNShape id="Event_1eshshx_di" bpmnElement="Event_1eshshx">
          <dc:Bounds x="292" y="102" width="36" height="36"/>
          <bpmndi:BPMNLabel>
            <dc:Bounds x="278" y="145" width="68" height="14"/>
          </bpmndi:BPMNLabel>
        </bpmndi:BPMNShape>
        <bpmndi:BPMNShape id="Activity_02f0tls_di" bpmnElement="Activity_02f0tls">
          <dc:Bounds x="380" y="80" width="100" height="80"/>
        </bpmndi:BPMNShape>
      </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
  </bpmn2:definitions>
