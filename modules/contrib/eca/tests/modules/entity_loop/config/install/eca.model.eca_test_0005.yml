langcode: en
status: true
dependencies:
  config:
    - eca.eca.eca_test_0005
id: eca_test_0005
label: Views
tags:
  - 'test models'
  - views
  - loop
  - 'custom event'
  - cron
  - 'switch user'
documentation: 'During cron, this model switches the user context, queries a view to receive all user accounts and loops over all those users triggering a custom event for each of them to print their names in messages.'
filename: ''
modeldata: |
  <?xml version="1.0" encoding="UTF-8"?>
  <bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:di="http://www.omg.org/spec/DD/********/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:qa="http://some-company/schema/bpmn/qa" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" xsi:schemaLocation="http://www.omg.org/spec/BPMN/********/MODEL BPMN20.xsd">
    <bpmn2:process id="eca_test_0005" name="Views" isExecutable="true" camunda:versionTag="1.0.0">
      <bpmn2:documentation>During cron, this model switches the user context, queries a view to receive all user accounts and loops over all those users triggering a custom event for each of them to print their names in messages.</bpmn2:documentation>
      <bpmn2:extensionElements>
        <camunda:properties>
          <camunda:property name="Tags" value="test models,views,loop,custom event,cron,switch user"/>
        </camunda:properties>
      </bpmn2:extensionElements>
      <bpmn2:startEvent id="Event_1up81dz" name="Cron" camunda:modelerTemplate="org.drupal.event.eca_base:eca_cron">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="eca_base:eca_cron"/>
          </camunda:properties>
          <camunda:field name="frequency">
            <camunda:string>* * * * *</camunda:string>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:outgoing>Flow_127rqxa</bpmn2:outgoing>
      </bpmn2:startEvent>
      <bpmn2:task id="Activity_08o1wo5" name="List users" camunda:modelerTemplate="org.drupal.action.eca_views_query">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="eca_views_query"/>
          </camunda:properties>
          <camunda:field name="token_name">
            <camunda:string>userlist</camunda:string>
          </camunda:field>
          <camunda:field name="view_id">
            <camunda:string>user_admin_people</camunda:string>
          </camunda:field>
          <camunda:field name="display_id">
            <camunda:string>default</camunda:string>
          </camunda:field>
          <camunda:field name="arguments">
            <camunda:string/>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:incoming>Flow_15gylqq</bpmn2:incoming>
        <bpmn2:outgoing>Flow_1hod3um</bpmn2:outgoing>
      </bpmn2:task>
      <bpmn2:task id="Activity_0ha3xin" name="Switch user" camunda:modelerTemplate="org.drupal.action.eca_switch_account">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="eca_switch_account"/>
          </camunda:properties>
          <camunda:field name="user_id">
            <camunda:string>1</camunda:string>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:incoming>Flow_127rqxa</bpmn2:incoming>
        <bpmn2:outgoing>Flow_15gylqq</bpmn2:outgoing>
      </bpmn2:task>
      <bpmn2:sequenceFlow id="Flow_127rqxa" sourceRef="Event_1up81dz" targetRef="Activity_0ha3xin"/>
      <bpmn2:sequenceFlow id="Flow_15gylqq" sourceRef="Activity_0ha3xin" targetRef="Activity_08o1wo5"/>
      <bpmn2:task id="Activity_105ssdc" name="Trigger X1" camunda:modelerTemplate="org.drupal.action.eca_trigger_content_entity_custom_event">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="eca_trigger_content_entity_custom_event"/>
          </camunda:properties>
          <camunda:field name="event_id">
            <camunda:string>x1</camunda:string>
          </camunda:field>
          <camunda:field name="object">
            <camunda:string>userlist</camunda:string>
          </camunda:field>
          <camunda:field name="tokens">
            <camunda:string/>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:incoming>Flow_1hod3um</bpmn2:incoming>
      </bpmn2:task>
      <bpmn2:sequenceFlow id="Flow_1hod3um" sourceRef="Activity_08o1wo5" targetRef="Activity_105ssdc"/>
      <bpmn2:startEvent id="Event_1asjw3k" name="X1" camunda:modelerTemplate="org.drupal.event.content_entity:custom">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="content_entity:custom"/>
          </camunda:properties>
          <camunda:field name="event_id">
            <camunda:string>x1</camunda:string>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:outgoing>Flow_1yl6zrs</bpmn2:outgoing>
      </bpmn2:startEvent>
      <bpmn2:task id="Activity_0xpew34" name="Msg" camunda:modelerTemplate="org.drupal.action.action_message_action">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="action_message_action"/>
          </camunda:properties>
          <camunda:field name="message">
            <camunda:string>User [entity:name]</camunda:string>
          </camunda:field>
        <camunda:field name="replace_tokens"><camunda:string>yes</camunda:string></camunda:field></bpmn2:extensionElements>
        <bpmn2:incoming>Flow_1yl6zrs</bpmn2:incoming>
      </bpmn2:task>
      <bpmn2:sequenceFlow id="Flow_1yl6zrs" sourceRef="Event_1asjw3k" targetRef="Activity_0xpew34"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="sid-2ba1f4cc-5448-48a2-9b04-b3b7577df2e7">
      <bpmndi:BPMNPlane id="sid-54972076-8b7f-42f0-ac38-e775d7c9542b" bpmnElement="eca_test_0005">
        <bpmndi:BPMNEdge id="Flow_1yl6zrs_di" bpmnElement="Flow_1yl6zrs">
          <di:waypoint x="358" y="410"/>
          <di:waypoint x="410" y="410"/>
        </bpmndi:BPMNEdge>
        <bpmndi:BPMNEdge id="Flow_1hod3um_di" bpmnElement="Flow_1hod3um">
          <di:waypoint x="510" y="160"/>
          <di:waypoint x="560" y="160"/>
        </bpmndi:BPMNEdge>
        <bpmndi:BPMNEdge id="Flow_15gylqq_di" bpmnElement="Flow_15gylqq">
          <di:waypoint x="460" y="230"/>
          <di:waypoint x="460" y="200"/>
        </bpmndi:BPMNEdge>
        <bpmndi:BPMNEdge id="Flow_127rqxa_di" bpmnElement="Flow_127rqxa">
          <di:waypoint x="358" y="160"/>
          <di:waypoint x="380" y="160"/>
          <di:waypoint x="380" y="270"/>
          <di:waypoint x="410" y="270"/>
        </bpmndi:BPMNEdge>
        <bpmndi:BPMNShape id="Event_1up81dz_di" bpmnElement="Event_1up81dz">
          <dc:Bounds x="322" y="142" width="36" height="36"/>
          <bpmndi:BPMNLabel>
            <dc:Bounds x="328" y="185" width="24" height="14"/>
          </bpmndi:BPMNLabel>
        </bpmndi:BPMNShape>
        <bpmndi:BPMNShape id="Activity_08o1wo5_di" bpmnElement="Activity_08o1wo5">
          <dc:Bounds x="410" y="120" width="100" height="80"/>
        </bpmndi:BPMNShape>
        <bpmndi:BPMNShape id="Activity_0ha3xin_di" bpmnElement="Activity_0ha3xin">
          <dc:Bounds x="410" y="230" width="100" height="80"/>
        </bpmndi:BPMNShape>
        <bpmndi:BPMNShape id="Activity_105ssdc_di" bpmnElement="Activity_105ssdc">
          <dc:Bounds x="560" y="120" width="100" height="80"/>
        </bpmndi:BPMNShape>
        <bpmndi:BPMNShape id="Event_1asjw3k_di" bpmnElement="Event_1asjw3k">
          <dc:Bounds x="322" y="392" width="36" height="36"/>
          <bpmndi:BPMNLabel>
            <dc:Bounds x="333" y="435" width="14" height="14"/>
          </bpmndi:BPMNLabel>
        </bpmndi:BPMNShape>
        <bpmndi:BPMNShape id="Activity_0xpew34_di" bpmnElement="Activity_0xpew34">
          <dc:Bounds x="410" y="370" width="100" height="80"/>
        </bpmndi:BPMNShape>
      </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
  </bpmn2:definitions>
