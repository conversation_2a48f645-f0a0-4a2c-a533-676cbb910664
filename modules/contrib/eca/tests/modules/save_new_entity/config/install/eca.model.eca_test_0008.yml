langcode: en
status: true
dependencies:
  config:
    - eca.eca.eca_test_0008
id: eca_test_0008
label: 'Save new entity'
tags:
  - 'test models'
  - 'create entity'
documentation: 'Create new tag with the same label as the triggering node.'
filename: ''
modeldata: |
  <?xml version="1.0" encoding="UTF-8"?>
  <bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:qa="http://some-company/schema/bpmn/qa" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="eca_test_0008" name="Save new entity" isExecutable="true" camunda:versionTag="1.0.0">
      <bpmn2:documentation>Create new tag with the same label as the triggering node.</bpmn2:documentation>
      <bpmn2:extensionElements>
        <camunda:properties>
          <camunda:property name="Tags" value="test models,create entity"/>
        </camunda:properties>
      </bpmn2:extensionElements>
      <bpmn2:startEvent id="insert_article" name="Node inserted" camunda:modelerTemplate="org.drupal.event.content_entity:insert">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="content_entity:insert"/>
          </camunda:properties>
          <camunda:field name="type">
            <camunda:string>node _all</camunda:string>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:outgoing>Flow_1opgjzh</bpmn2:outgoing>
      </bpmn2:startEvent>
      <bpmn2:task id="Activity_0rt55gb" name="Load node" camunda:modelerTemplate="org.drupal.action.eca_token_load_entity">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="eca_token_load_entity"/>
          </camunda:properties>
          <camunda:field name="token_name">
            <camunda:string>article</camunda:string>
          </camunda:field>
          <camunda:field name="from">
            <camunda:string>current</camunda:string>
          </camunda:field>
          <camunda:field name="entity_type">
            <camunda:string>_none</camunda:string>
          </camunda:field>
          <camunda:field name="entity_id">
            <camunda:string/>
          </camunda:field>
          <camunda:field name="revision_id">
            <camunda:string/>
          </camunda:field>
          <camunda:field name="langcode">
            <camunda:string>_interface</camunda:string>
          </camunda:field>
          <camunda:field name="latest_revision">
            <camunda:string>no</camunda:string>
          </camunda:field>
          <camunda:field name="unchanged">
            <camunda:string>no</camunda:string>
          </camunda:field>
          <camunda:field name="object">
            <camunda:string/>
          </camunda:field>
          <camunda:field name="properties">
            <camunda:string/>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:incoming>Flow_1opgjzh</bpmn2:incoming>
        <bpmn2:outgoing>Flow_1krl1hn</bpmn2:outgoing>
      </bpmn2:task>
      <bpmn2:sequenceFlow id="Flow_1opgjzh" sourceRef="insert_article" targetRef="Activity_0rt55gb"/>
      <bpmn2:task id="create_tag" name="Create new tag" camunda:modelerTemplate="org.drupal.action.eca_new_entity">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="eca_new_entity"/>
          </camunda:properties>
          <camunda:field name="token_name">
            <camunda:string>tag</camunda:string>
          </camunda:field>
          <camunda:field name="type">
            <camunda:string>taxonomy_term tags</camunda:string>
          </camunda:field>
          <camunda:field name="langcode">
            <camunda:string/>
          </camunda:field>
          <camunda:field name="label">
            <camunda:string>Tag: [article:title]</camunda:string>
          </camunda:field>
          <camunda:field name="published">
            <camunda:string>yes</camunda:string>
          </camunda:field>
          <camunda:field name="owner">
            <camunda:string/>
          </camunda:field>
          <camunda:field name="object">
            <camunda:string/>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:incoming>Flow_1krl1hn</bpmn2:incoming>
        <bpmn2:outgoing>Flow_02t0xvi</bpmn2:outgoing>
      </bpmn2:task>
      <bpmn2:sequenceFlow id="Flow_1krl1hn" sourceRef="Activity_0rt55gb" targetRef="create_tag"/>
      <bpmn2:task id="save_tag" name="Save tag" camunda:modelerTemplate="org.drupal.action.eca_save_entity">
        <bpmn2:extensionElements>
          <camunda:properties>
            <camunda:property name="pluginid" value="eca_save_entity"/>
          </camunda:properties>
          <camunda:field name="object">
            <camunda:string>tag</camunda:string>
          </camunda:field>
        </bpmn2:extensionElements>
        <bpmn2:incoming>Flow_02t0xvi</bpmn2:incoming>
      </bpmn2:task>
      <bpmn2:sequenceFlow id="Flow_02t0xvi" sourceRef="create_tag" targetRef="save_tag"/>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="sid-0a76329c-a777-4f6b-8942-89034a8dddd8">
      <bpmndi:BPMNPlane id="sid-95105220-93cf-4cbf-ad85-491d56ec5629" bpmnElement="eca_test_0008">
        <bpmndi:BPMNEdge id="Flow_02t0xvi_di" bpmnElement="Flow_02t0xvi">
          <di:waypoint x="550" y="190"/>
          <di:waypoint x="610" y="190"/>
        </bpmndi:BPMNEdge>
        <bpmndi:BPMNEdge id="Flow_1krl1hn_di" bpmnElement="Flow_1krl1hn">
          <di:waypoint x="390" y="190"/>
          <di:waypoint x="450" y="190"/>
        </bpmndi:BPMNEdge>
        <bpmndi:BPMNEdge id="Flow_1opgjzh_di" bpmnElement="Flow_1opgjzh">
          <di:waypoint x="228" y="190"/>
          <di:waypoint x="290" y="190"/>
        </bpmndi:BPMNEdge>
        <bpmndi:BPMNShape id="Event_0zvt8vz_di" bpmnElement="insert_article">
          <dc:Bounds x="192" y="172" width="36" height="36"/>
          <bpmndi:BPMNLabel>
            <dc:Bounds x="176" y="215" width="69" height="14"/>
          </bpmndi:BPMNLabel>
        </bpmndi:BPMNShape>
        <bpmndi:BPMNShape id="Activity_0rt55gb_di" bpmnElement="Activity_0rt55gb">
          <dc:Bounds x="290" y="150" width="100" height="80"/>
          <bpmndi:BPMNLabel/>
        </bpmndi:BPMNShape>
        <bpmndi:BPMNShape id="Activity_1vtmrqj_di" bpmnElement="create_tag">
          <dc:Bounds x="450" y="150" width="100" height="80"/>
          <bpmndi:BPMNLabel/>
        </bpmndi:BPMNShape>
        <bpmndi:BPMNShape id="Activity_1wz07c6_di" bpmnElement="save_tag">
          <dc:Bounds x="610" y="150" width="100" height="80"/>
          <bpmndi:BPMNLabel/>
        </bpmndi:BPMNShape>
      </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
  </bpmn2:definitions>
