langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_terms
    - node.type.type_set_field_value
    - taxonomy.vocabulary.tags
id: node.type_set_field_value.field_terms
field_name: field_terms
entity_type: node
bundle: type_set_field_value
label: Terms
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      tags: tags
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
