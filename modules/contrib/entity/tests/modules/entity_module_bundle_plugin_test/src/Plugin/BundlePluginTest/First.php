<?php

namespace Drupal\entity_module_bundle_plugin_test\Plugin\BundlePluginTest;

use <PERSON><PERSON><PERSON>\entity\BundleFieldDefinition;
use <PERSON><PERSON>al\Core\Plugin\PluginBase;

/**
 * Provides the first bundle plugin.
 *
 * @BundlePluginTest(
 *   id = "first",
 *   label = @Translation("First"),
 *   description = @Translation("Some description"),
 * )
 */
class First extends PluginBase implements BundlePluginTestInterface {

  /**
   * {@inheritdoc}
   */
  public function buildFieldDefinitions() {
    $fields = [];
    $fields['first_mail'] = BundleFieldDefinition::create('email')
      ->setLabel($this->t('Email'))
      ->setRequired(TRUE);

    return $fields;
  }

}
