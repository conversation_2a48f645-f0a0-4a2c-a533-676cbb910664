<?php

namespace Dr<PERSON>al\entity_module_test\Field;

use <PERSON>upal\Core\Field\FieldItemList;
use Drupal\Core\TypedData\ComputedItemListTrait;

/**
 * Item list for the computed_entity_ref computed entity reference field.
 */
class ComputedEntityRefItemList extends FieldItemList {

  use ComputedItemListTrait;

  /**
   * {@inheritdoc}
   */
  protected function computeValue() {
    // Always make the field value empty.
    $this->list = [];
  }
}
