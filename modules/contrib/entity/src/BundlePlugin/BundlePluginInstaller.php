<?php

namespace Dr<PERSON>al\entity\BundlePlugin;

use <PERSON><PERSON>al\Core\Entity\EntityBundleListenerInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Field\FieldDefinitionListenerInterface;
use Drupal\Core\Field\FieldStorageDefinitionListenerInterface;

class BundlePluginInstaller implements BundlePluginInstallerInterface {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The entity bundle listener.
   *
   * @var \Drupal\Core\Entity\EntityBundleListenerInterface
   */
  protected $entityBundleListener;

  /**
   * The field storage definition listener.
   *
   * @var \Drupal\Core\Field\FieldStorageDefinitionListenerInterface
   */
  protected $fieldStorageDefinitionListener;

  /**
   * The field definition listener.
   *
   * @var \Drupal\Core\Field\FieldDefinitionListenerInterface
   */
  protected $fieldDefinitionListener;

  /**
   * Constructs a new BundlePluginInstaller object.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Entity\EntityBundleListenerInterface $entity_bundle_listener
   *   The entity bundle listener.
   * @param \Drupal\Core\Field\FieldStorageDefinitionListenerInterface $field_storage_definition_listener
   *   The field storage definition listener.
   * @param \Drupal\Core\Field\FieldDefinitionListenerInterface $field_definition_listener
   *   The field definition listener.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager, EntityBundleListenerInterface $entity_bundle_listener, FieldStorageDefinitionListenerInterface $field_storage_definition_listener, FieldDefinitionListenerInterface $field_definition_listener) {
    $this->entityTypeManager = $entity_type_manager;
    $this->entityBundleListener = $entity_bundle_listener;
    $this->fieldStorageDefinitionListener = $field_storage_definition_listener;
    $this->fieldDefinitionListener = $field_definition_listener;
  }

  /**
   * {@inheritdoc}
   */
  public function installBundles(EntityTypeInterface $entity_type, array $modules) {
    $bundle_handler = $this->entityTypeManager->getHandler($entity_type->id(), 'bundle_plugin');
    $bundles = array_filter($bundle_handler->getBundleInfo(), function ($bundle_info) use ($modules) {
      return in_array($bundle_info['provider'], $modules, TRUE);
    });
    foreach (array_keys($bundles) as $bundle) {
      $this->entityBundleListener->onBundleCreate($bundle, $entity_type->id());
      foreach ($bundle_handler->getFieldDefinitions($bundle) as $definition) {
        $this->fieldStorageDefinitionListener->onFieldStorageDefinitionCreate($definition);
        $this->fieldDefinitionListener->onFieldDefinitionCreate($definition);
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  public function uninstallBundles(EntityTypeInterface $entity_type, array $modules) {
    $bundle_handler = $this->entityTypeManager->getHandler($entity_type->id(), 'bundle_plugin');
    $bundles = array_filter($bundle_handler->getBundleInfo(), function ($bundle_info) use ($modules) {
      return in_array($bundle_info['provider'], $modules, TRUE);
    });

    /**
     * We need to uninstall the field storage definitions in a separate loop.
     *
     * This way we can allow a module to re-use the same field within multiple
     * bundles, allowing e.g to subclass a bundle plugin.
     *
     * @var \Drupal\entity\BundleFieldDefinition[] $field_storage_definitions
     */
    $field_storage_definitions = [];

    foreach (array_keys($bundles) as $bundle) {
      $this->entityBundleListener->onBundleDelete($bundle, $entity_type->id());
      foreach ($bundle_handler->getFieldDefinitions($bundle) as $definition) {
        $this->fieldDefinitionListener->onFieldDefinitionDelete($definition);
        $field_storage_definitions[$definition->getName()] = $definition;
      }
    }

    foreach ($field_storage_definitions as $definition) {
      $this->fieldStorageDefinitionListener->onFieldStorageDefinitionDelete($definition);
    }
  }

}
